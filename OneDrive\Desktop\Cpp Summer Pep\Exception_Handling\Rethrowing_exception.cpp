// Rethrowing exception

#include<bits/stdc++.h>
using namespace std;

int main(){
    int a;
    cin >> a;
    try{
        try{
            if(a == 1){
                throw "Exception caught , Value of a : 1";
            }
            else{
                cout << "value of a = " << a << endl;
            }
        }
        catch(const char e){
            cout << "Inner catch block" << endl;
            cout << e << endl;
            throw e;
        }
    }
    catch(const char e){
        cout << "Outer catch block" << endl;
        cout << e <<endl;
    }
    return 0;
}