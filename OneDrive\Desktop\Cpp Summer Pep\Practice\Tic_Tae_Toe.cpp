// Tic <PERSON> toe game

#include<bits/stdc++.h>
using namespace std;

// Player class to handle player
class Player{
    private:
    char symbol;
    string name;
    public:
    Player(char x , string n){
        symbol = x;
        name = n;
    }
    char getsymbol(){
        return symbol;
    }
    string getname(){
        return name;
    }
};

// Borad to maage game board
class Board{
    private:
    char board[3][3];
    int counter;
    public:
    Board(){
        counter = 0;
        for(int i = 0 ; i < 3 ; i++){
            for(int j = 0 ; j < 3 ; j++){
                board[i][j] = ' '; 
            }
        }
    }

    // Function to draw Board
    void drawboard(){
        cout << "--------------" << endl;
        for(int i = 0 ; i < 3 ; i++){
            cout << "| ";
            for(int j = 0 ; j < 3 ; j++){
                cout << board[i][j] << "| ";
            }
            cout << "--------------" << endl;
        }
    }
    // Function to check move is valid or not
    bool isvalid(int row , int column){
        return (row >= 0 && row < 3 && column >= 0 && column < 3 && board[row][column] = ' ');
    }

    // Function to make a move
    void makemove(int row , int column , int symbol){
        if(isvalid(row , column)){
            board[row][column] == symbol;
            counter++;
        }
    }

};


bool isTie(){
    if(iswinner()){
        cout << "You have won the match" << endl;
    }
    else{
        for(int i = 0 ; i < 3 ; i++){
            for(int j = 0 ; j < 3 ; j++){
                if(board[i][j] == ' '){
                    
                }
            }
        }
    }
}
int main(){
    return 0;
}