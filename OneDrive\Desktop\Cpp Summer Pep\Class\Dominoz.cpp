#include<iostream>
using namespace std;

class Dominos{
    public:
    void menu(){
        cout << "Displaying menu : " << endl;
        cout << 1 << " Pizza" << endl;
        cout << 2 << " Burger" << endl;
        cout << 3 << " French fries" << endl;
        cout << 4 << " Mega meal" << endl;
        cout << 5 << " Pizza + burger + coke" << endl;
        cout << 6 << " Pizza combo meal (Small + medium + large pizza's + coke )" << endl;
    }

    void prices(int n){
        switch(n){
            case 1:
            cout << "Item price : " << "100 rupees" << endl;
            break;

            case 2:
            cout << "Item price : " << "50 rupees" << endl;
            break;

            case 3:
            cout << "Item price : " << "80 rupees" << endl;
            break;

            case 4:
            cout << "Item price : " << "200 rupees" << endl;
            break;

            case 5:
            cout << "Item price : " << "160 rupees" << endl;
            break;

            case 6:
            cout << "Item price : " << "250 rupees" << endl;
            break;

            default:
            cout << "Order number not matched , try again!" << endl;
        }
    }

};

int main(){
    int ordernum;
    char ch;
    Dominos obj;
    cout << "Choose menu number" << endl;
    obj.menu();

    cin >> ordernum;

    obj.prices(ordernum);

    cout << "Confirm order (Y/N) : ";
    cin >> ch;

    if(ch == 'Y'){
        cout << "Order confirmed" << endl;
    }
    else{
        cout << "Thank You for using our services " << endl;
    }
    return 0;
}