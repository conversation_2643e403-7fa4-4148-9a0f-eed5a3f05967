#include<bits/stdc++.h>
using namespace std;

int main(){
    int n , m;
    cout << "rows : ";
    cin >> n;
    cout << "columns : ";
    cin >> m;
    int **arr = new int*[n];

    for(int i = 0 ; i < n ; i++){
        arr[i] = new int[m];
    }

    for(int i = 0 ; i < n ; i++){
        for(int j = 0 ; j < m ; j++){
            cin >> arr[i][j];
        }
    }

    for(int i = 0 ; i < n ; i++){
        for(int j = 0 ; j < m ; j++){
            cout << arr[i][j] << ' ';
        }
        cout << endl;
    }

    return 0;
}