// Pattern Printing
//     &
//    & &
//   &   &
//  &     &
// &&&&&&&&&

#include<bits/stdc++.h>
using namespace std;

int main(){
    int n;
    cout << "Enter no. of rows : ";
    cin >> n;
    for(int i = 1 ; i <= n ; i++){
        for(int j = i ; j < n ; j++){
            cout << ' ';
        }
        
        if(i == 1) cout << '&';
        else if(i == n){
            for(int m = 1 ; m <= 2*n-1 ; m++){
                cout << '&';
            }
        }
        else{
            cout << '&';
            for(int p = 1 ; p <= 2*(i-1)-1 ; p++){
                cout << ' ';
            }
            cout << '&';
        }
        cout << endl;
    }
    return 0;
}