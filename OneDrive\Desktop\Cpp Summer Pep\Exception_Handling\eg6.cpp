// Rethrowing exception

#include<bits/stdc++.h>
using namespace std;

int main(){
    int a = 1;
    try{
        try{
            if(a == 1){
                throw a;
            }
        }
        catch(int a){
            cout << "Inner catch block exception" << endl;
            throw 's';
        }
    }
    catch(char s){
        cout << "outer catch block exception" << endl;
    }
    return 0;
}