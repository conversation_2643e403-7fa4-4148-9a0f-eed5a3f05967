#include<iostream>
using namespace std;

int main(){
    int a , b , c ;
    cout << "Enter three sides of the right traingle : " << endl;
    cout << "Enter Base : ";
    cin >> a;
    cout << "Enter Perpendicular : ";
    cin >> b;
    cout << "Enter Hypotenuse : ";
    cin >> c;
    if((a + b) < c || (b + c) < a || (c + a) < b){
        cout << "Sides are not valid" << endl;
    }
    else{
        if(c*c == a*a + b*b){
            cout << "Traingle is right angled" << endl;
        }
        else{
            cout << "traingle is notright angled" << endl;
        }
    }
    return 0;
}