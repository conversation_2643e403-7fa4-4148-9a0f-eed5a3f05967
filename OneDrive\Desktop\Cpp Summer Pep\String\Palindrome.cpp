#include<bits/stdc++.h>
using namespace std;

int main(){
    string str;
    cin >> str;
    int n = str.length()-1;
    bool res = true;
    int i = 0;
    while(i <= n){
        if(str[i] == str[n]){
            ++i;
            --n;
        }
        else{
            res = false;
            break;
        }
    }
    if(res) cout << "Palindrome" << endl;
    else{
        cout << "Not palindrome" << endl;
    }
    return 0;
}