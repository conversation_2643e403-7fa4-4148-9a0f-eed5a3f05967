#include<bits/stdc++.h>
using namespace std;

class Base{
    string name , bname , ed , rd;
    public:

    void getdata(){
        cout << "Enter name of person : " << endl;
        getline(cin , name);
        cout << "Enter name of Book : " << endl;
        getline(cin , bname);
        cout << "Enter date if issuing : " << endl;
        getline(cin , ed);
        cout << "Enter date if returning : " << endl;
        getline(cin , rd);

        ofstream file("eg.txt", ios::app);
        if (file.is_open()) {
            file << "Name: " << name << ", Book: " << bname
                 << ", Issue Date: " << ed << ", Return Date: " << rd << endl;
            file.close();
        } else {
            cout << "Unable to open file." << endl;
        }
    }

};

class child : public Base{
    public:
    void putdata(){
        cout << "Information of the user has been saved ." << endl;
    }
};
int main(){
    Base obj;
    obj.getdata();
    child obj1;
    obj1.putdata();
    return 0;
}