#include<bits/stdc++.h>
using namespace std;

int main(){
    unordered_map<char, int> mp;
    string word;
    cout << "Input a word : " << endl;
    cin >> word;
    for(int i = 0 ; i < word.length() ; i++){
        mp[word[i]]++;
    }
    cout << "Printing frequency : " << endl;
    for(auto& i : mp){
        cout << i.first << ' ' << i.second << endl;
    }
    // for(auto& {key , val} : mp){
    //     cout << key << ' ' << val << endl;
    // }
    for(auto i = mp.begin() ; i != mp.end() ; i++){
        cout << 
    }
    return 0;
}