// Implement a bank account class
// Deposit , wihtdraw

#include<iostream>
using  namespace std;

class BankAccount{
    private:
    float total;
    float init;

    public:
    BankAccount(float a){
        init = a;
    }

    void Deposit(float amount1){
        cout << "Amount Deposited : " << amount1 << endl;
        total = init + amount1;
        cout << "Total Amount : " << total << endl;
    }
    void Withdrawn(float amount2){
        cout << "Amount Withdrawn : " << amount2 << endl;
        total = total - amount2;
        if(total < 0){
            cout << "Cant withdraw , must be lower than : " << init << endl;
        }
        else{
            cout << "Total Amount : " << total << endl;
        }
    }
};
int main(){
    BankAccount obj(100);
    obj.Deposit(1000);
    obj.Withdrawn(700);
    return 0;
}