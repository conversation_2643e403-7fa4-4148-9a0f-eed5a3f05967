#include<bits/stdc++.h>
using namespace std;

int main(){
    int n , d = 0, temp , temp2 , res = 0;
    cout << "Enter a number : " ;
    cin >> n;
    temp = n;
    while(temp != 0){
        temp = temp / 10;
        d++;
    }
    temp2 = n;
    while(temp2 != 0){
        temp = temp2 % 10;
        res += (int)(round(pow(temp , d)));
        temp2 = temp2 / 10;
    }
    if(n == res){
        cout << "Armstrong" << endl;
    }
    else{
        cout << "Not Armstrong" << endl;
    }
    return 0;
}