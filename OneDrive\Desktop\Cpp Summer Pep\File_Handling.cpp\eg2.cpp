int main(){
    ifstream inClientFile("clients.txt",ios::in);
    if(!inClientFile){
        cout<<""
    }
    while(request!=END){
        sdwitch(request){
            case ZERO:
            cout<<"\nAccounts with zero balance:\n";
            break;
            case CREDIT:
            cout<<"\nAccounts with credit balance:\n";
            case DEBIT:
            cout<<"\nAccount with debit balance:\n";
            break;
        }
        inClientFil>>account>>name>>balance;
        while(!inClientFile.eof()){
            if(shoulddisplay(request,balance))
            cout<<acount<<''<<name<<''<<balance<<endl;
            inClientFile>>acount>>name>>balance;
        }
        inClientFile.clear()
        inClientFile.seekg(0);
        request=getRequest()
        int getRequest(){
            int choie;
            cout<<"enter 0 to see zero balance account"<<endl;
            cout<<"enter 1 to see credit balance account"<<endl;
            cout<<"enter 2 to see debit balance account"<<endl;
            cout<<"enter 3 to see end program"<<endl;
            cin>>choice;
            return choice;

        }
        bool shoulddisplay(int req,double bal){
            if((req==ZERO && bal==0)||(req==CREDIT && bal<0)||(req==DEBIT && bal>0))
            return true;
            else return false;
        }
        
    }
 }