#include<iostream>
using namespace std;

int BinarySearch(int arr[] , int x , int n){
    int l = 0;
    int r = n-1;
    int mid = l + (r - l)/2;
    while(l < r){
        if(arr[mid] > x){
            l = mid;
        }
        else if(arr[mid] < x){
            r = mid + 1;
        }
        else{
            return 1;
        }
    }
    return -1;
}

int main(){
    int n;
    cout << "Enter size of the array : " << endl;
    cin >> n;
    int arr[n];
    for(int i = 0 ; i < n ; i++){
        cin >> arr[i];
    }
    int x;
    cout << "enter element to search for : " << endl;
    cin >> x;

    int res = BinarySearch(arr , x , n);
    if(res){
        cout << "Element found " << endl;
    }
    else{
        cout << "Element not found " << endl;
    }
    return 0;
}

//  1 2 3 4 5 
// 2