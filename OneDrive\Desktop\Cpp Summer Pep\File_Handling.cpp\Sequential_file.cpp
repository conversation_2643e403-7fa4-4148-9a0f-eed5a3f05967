// Create a sequential file

#include<bits/stdc++.h>
using namespace std;

int main(){
    ofstream outClinetFile("clients.txt" , ios::out);
    if(!outClinetFile){
        cout << "File could not be openend" << endl;
        exit(1);
    }
    cout << "Enter the account , name , and balance" << endl;
    cout << "Enter \'N\' to end input. \n";

    int account;
    char name[30] , ch='y';
    double balance;
    while(ch =='y'){
        cin >> account >> name >> balance;
        outClinetFile <<account << ' ' << name << ' ' << balance << endl;
        cout << "?";
        cin >> ch;
    }
    return 0;
}