#include<iostream>
#include<thread>
#include<chrono>
using namespace std;

void printMessage(string message , int delay){
    for(int i = 0 ; i < 5 ; i++){
        cout << message << " count : " << i << endl;
        this_thread::sleep_for(chrono::milliseconds(delay));
    }
}
int main(){
    thread t1(printMessage, "thread 1" , 500);
    thread t2(printMessage, "thread 2" , 300);
    thread t3(printMessage, "thread 3" , 700);

    t1.join();
    t2.join();
    t3.join();

    cout << "All threads finished execution ." << endl;

    return 0;
}