#include<bits/stdc++.h>
using namespace std;

int Binarysearch(int arr[] , int n , int x){
    int l = 0;
    int r = n - 1;
    while(l <= r){
        int mid = l - (l - r)/2;
        if(arr[mid] == x){
            return 1;
        }
        else if(arr[mid] > x){
            r = mid - 1;
        }
        else{
            l = mid + 1;
        }
    }
    return -1;
}
int main(){
    int n;
    cin >> n;
    int arr[n];
    for(int i = 0 ; i < n ; i++){
        cin >> arr[i];
    }
    int x;
    cin >> x;
    int res = Binarysearch(arr , n , x);
    if(res == 1){
        cout << "Element Found" << endl;
    }
    else{
        cout << "Element Not Found" << endl;
    }
    return 0;
}