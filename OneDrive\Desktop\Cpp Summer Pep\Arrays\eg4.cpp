#include<bits/stdc++.h>
using namespace std;

int main(){
    int arr[5];
    for(int i = 0 ; i < 5 ; i++){
        cin >> arr[i];
    }
    int temp = INT_MIN;
    int res = INT_MIN;
    for(int i = 0 ; i < 5 ; i++){
        if(arr[i] > temp){
            res = temp;
            temp = arr[i];
        }
        else if(arr[i] > res && arr[i] != temp){
            res = arr[i];
        }
    }
    cout << "2nd Maximum element is : " << res << endl;
    return 0;
}