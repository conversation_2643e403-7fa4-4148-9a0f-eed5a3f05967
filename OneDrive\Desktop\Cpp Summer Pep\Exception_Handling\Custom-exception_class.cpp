// Custom exception class

#include<bits/stdc++.h>
using namespace std;

class CustomException{
    public:
    CustomException(int a){
        try{
            if(a <= 0){
                throw "Exception caught , value of a is <= 0";
            }
            else{
                cout << "Value of a : " << a << endl;
            }
        }
        catch(const char *e){
            cout << e << endl;
        }
    }
};
int main(){
    CustomException obj(-2);
    return 0;
}