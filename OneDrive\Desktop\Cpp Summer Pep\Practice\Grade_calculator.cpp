// Student Grade Calculator

#include<bits/stdc++.h>
using namespace std;

class GradeCalculator{
    public:
    void getinfo(){
    string arr[3];
    cout << "Enter the names of student : " << endl;
    for(int i = 0 ; i < 3 ; i++){
        cin >> arr[i];
    }
    int marks[3][3];
    for(int i = 0 ; i < 3 ; i++){
        cout << "Enter marks of " << arr[i] << " for Science , English , Maths ." << endl;
        for(int j = 0 ; j < 3 ; j++){
            cin >> marks[i][j];
        }
    }
    calculator(arr , marks);
    }

    void calculator(string arr[3] , int marks[3][3]){
        int sum = 0;
        for(int i = 0 ; i < 3 ; i++){
            cout << "Percentage and Grade of " << arr[i] << endl;
            for(int j = 0 ; j < 3 ; j++){
                sum += marks[i][j];
            }
            float res = sum / 3;
            cout << res << '%' << ' ';
            if(res >= 90){
                cout << "Grade O" << endl;
            }
            else if(res >= 80){
                cout << "Grade A+" << endl;
            }
            else if(res >= 70){
                cout << "Grade A" << endl;
            }
            else if(res >= 60){
                cout << "Grade B+" << endl;
            }
            else if(res >= 50){
                cout << "Grade B" << endl;
            }
            else if(res >= 40){
                cout << "Grade C+" << endl;
            }
            else if(res >= 30){
                cout << "Grade C" << endl;
            }
            else{
                cout << "failed " << endl;
            }
            sum = 0;
        }
    }
};
int main(){
    GradeCalculator obj;
    obj.getinfo();
    return 0;
}