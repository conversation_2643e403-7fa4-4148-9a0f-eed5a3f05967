// DOuble Divison

#include<bits/stdc++.h>
using namespace std;

double division(double a, double b){
    if(b == 0){
        throw "Division by zero , not possible !";
    }
    else{
        return a / b;
    }
}
int main(){
    double a , b;
    cin >> a >> b;
    try{
        double res = division(a , b);
        cout << "res : " << res << endl;
    }
    catch(const char *e){
        cout << e << endl;
    }
    return 0;
}