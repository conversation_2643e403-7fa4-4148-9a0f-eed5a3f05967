#include<bits/stdc++.h>
using namespace std;

int main(){
    int a , n ;
    cin >> a;
    try{
        if(a == 1){
            throw "Exception caught , a : 1";
        }
        else if(a == 2){
            throw 'e';
        }
        else if(a == 3){
            throw n;
        }
        else{
            cout << "Value of a : " << a << endl;
        }
    }
    catch(const char *e){
        cout << e << endl;
    }
    catch(const char e){
        cout << "Exception caught , a : 2" << endl;
    }
    catch(const int n){
        cout << "Exception caught , a : 3" << endl;
    }
    return 0;
}