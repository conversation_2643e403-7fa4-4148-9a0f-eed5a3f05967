#include<bits/stdc++.h>
using namespace std;

int main(){
    char ch[50];
    gets(ch);
    int count1 = 0 , count2 = 0;
    for(int i = 0 ; ch[i] != '\0' ; i++){
        if(ch[i] == 'a' || ch[i] == 'A' || ch[i] == 'e' || ch[i] == 'E' || ch[i] == 'o' || ch[i] == 'O' || ch[i] == 'i' || ch[i] == 'I' || ch[i] == 'u' || ch[i] == 'U'){
            count1++;
        }
        else{
            count2++;
        }
    }
    cout << "Vowels : " << count1 << endl;
    cout << "Consonant : " << count2 << endl;
    return 0;
}