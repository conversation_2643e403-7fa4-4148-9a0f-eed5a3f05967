#include<bits/stdc++.h>
using namespace std;

int main(){
    int a , b;
    cin >> a >> b;
    try{
        if(b != 0){
            float div = (float)a / b;
            if(div < 0)
            {
                throw 'e';
            }
            else{
            cout << "a / b : " << div << endl;
            }
        }
        else{
            throw b;
        }
    }
    catch(int n){
        cout << "Exception occured , can't divide by : " << n << endl;
    }
    catch(char e){
        cout << "Exception occured , division is less than 1 "<< endl;
    }
    catch(...){
        cout << "Unknown exception"<< endl;
    }
    return 0;
}