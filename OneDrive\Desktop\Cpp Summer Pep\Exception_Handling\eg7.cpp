// Custom exception

#include<bits/stdc++.h>
using namespace std;

class demo{
    public:
    int num;

    public :
    demo(int x){
        try{
            if(x == 0){
                throw "zero not allowed";
            }
            num = x;
            show();
        }
        catch(char e){
            cout << "Exception caught" << endl;
            cout << e << endl;
        }
    }
    void show(){
        cout << "Num : " << num << endl;
    }
};

int main(){
    demo obj = demo(0);
}