#include<bits/stdc++.h>
using namespace std;

int main(){
    string s;
    getline(cin , s);
    int max_len = 0;
    int temp = 0;
    for(int i = 0 ; i < s.length() ; i++){
        if(s[i] != ' '){
            temp++;
            continue;            
        }
        max_len = max(temp , max_len);
        temp = 0;
    }
    cout << max_len;
    return 0;
}


// Sorting string alphabetically
// Binary search in a sorted array
// Leetcode 75
// Leetcode 47