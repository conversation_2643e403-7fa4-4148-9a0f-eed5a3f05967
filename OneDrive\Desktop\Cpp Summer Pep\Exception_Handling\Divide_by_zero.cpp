// Divide by zero exception

#include<bits/stdc++.h>
using namespace std;

int main(){
    int a , b;
    cin >> a >> b;
    try{
        if(b == 0){
            throw "Division by zero not possible";
        }
        else{
            int res = a / b;
            cout << "res : " << res << endl;
        }
    }
    catch(const char *e){
        cout << "Exception caught" << endl;
        cout << e << endl;
    }
    return 0;
}